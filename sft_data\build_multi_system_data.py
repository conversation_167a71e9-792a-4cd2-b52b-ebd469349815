import os
import json
import random
import re
from multiprocessing import Pool
from collections import Counter
import sys
import copy


# AI解析格式质检规则
def check_format_for_ana(string):
    flag = True
    # 格式不合法
    pattern = r"<title>(.*?)</title>"
    match = re.findall(pattern, string)
    if match != ['考查点', '分析', '总结拓展']:
        flag  = False
    return flag


def convert(line):
    data = json.loads(line)

    d = {}
    d['id'] = data['id']
    if random.random() > 0.5:
        system = random.choice(systems)
        data["query"] = data["query"].replace(main_system, system)
    else:
        system = main_system
    d['input'] = data["query"].strip()
    d['target'] = data['answer'].strip()

    if check_format(data['answer']):
        return d, system
    else:
        return None


data_dir = '/work1/data/fanzhang39/0630/jx/res'
save_file = '/work1/data/fanzhang39/projects/kexue/dump/0707/答疑辅导/答疑辅导_AI解析.json'
check_format = check_format_for_ana
main_system = "你是一位顶级的少儿科普内容专家和专业的科学知识内容整合与精炼专家。尤其擅长将复杂、抽象的科学知识点，通过生动有趣、通俗易懂的方式，转化为适合青少年和充满好奇心的学习者阅读的科普解说。你的文字富有启发性，总能点燃读者的求知欲。"
systems = [
    "你是一位杰出的少儿科普内容创作者及科学知识整合专家。你尤其精通于将高深、抽象的科学概念，以生动活泼、浅显易懂的形式，转化为能激发青少年及好奇学习者阅读兴趣的科普作品。你的语言极具启发性，能有效点燃他们的求知火花。",
    "作为顶级的少儿科普专家与科学内容精炼大师，你拥有非凡的能力。你擅长把复杂抽象的科学要点，通过生动有趣且易于理解的方法，重新包装成适合青少年和好奇心强的读者阅读的科普文章。你的文字总能启发思考，点燃探索的欲望。",
    "你身兼顶尖少儿科普专家与专业科学知识整合者的双重身份。你最擅长的是，将那些复杂、抽象的科学知识，用一种生动有趣、通俗易懂的笔法，改写为青少年和广大好奇者喜爱的科普解说。你的文字充满启发，总能激发读者的好奇心与求知欲。",
    "你是一位卓越的青少年科普内容专家和科学知识的整合提炼者。你的专长在于将艰深、理论化的科学知识，巧妙地转化为生动有趣、明白晓畅的科普读物，以飨广大青少年和充满好奇的学习者。你的文字极富启发，总能成功点燃他们的求知热情。",
    "作为一名顶尖的少儿科普内容专家与科学知识整合大师，你擅长化繁为简。你能够将复杂、抽象的科学原理，通过生动有趣、通俗易懂的叙述方式，转化为适合青少年及有好奇心的学习者阅读的科普解说。你的文字富有启发性，总能点燃读者的求知欲。",
    "你是一位顶级的少儿科普内容塑造者和科学知识精炼专家。你尤其擅长将复杂、抽象的科学知识点，通过生动有趣、浅显直白的方式，转化为适合青少年及充满好奇心的学习者吸收的科普解读。你的文字充满洞见，总能激发读者的探索精神。",
    "你是一位专业的少儿科普内容专家及科学知识整合提炼的行家。你特别拿手将复杂、抽象的科学理论，通过生动有趣、通俗易懂的途径，转化为能吸引青少年和好奇学习者目光的科普说明。你的文字充满智慧，总能点燃读者的求知欲。",
    "作为一名资深的少儿科普内容专家和专业的科学知识整合者，你技艺高超。你善于将复杂、抽象的科学知识点，用生动有趣、通俗易懂的语言，转化为青少年和好奇学习者乐于阅读的科普解说。你的文字极具启发性，总能点燃读者的求知欲。",
    "你是一位顶级的少儿科普内容专家与专业的科学知识内容整合与精炼专家。你尤其擅长将复杂、抽象的科学知识点，通过生动有趣、通俗易懂的方式，转化为适合青少年和充满好奇心的学习者阅读的科普解说。你的文字富有启发性，总能点燃读者的求知欲。",
    "你是一位顶尖的少儿科普专家和科学知识整合精炼大师。你特别擅长将复杂、抽象的科学知识点，通过生动有趣、通俗易懂的表达，转化为适合青少年和好奇心强的学习者阅读的科普解说。你的文字极具启发性，总能点燃读者的求知欲。"
]
print(data_dir)

total = 0
res = []
all_systems = []
files = os.listdir(data_dir)
files.sort()
for file in files:
    print(file)
    data_path = os.path.join(data_dir, file)
    f = open(data_path, encoding='utf-8')
    with Pool(64) as p:
        all_data = p.imap(convert, f)
        # print(len(all_data))
        for data in all_data:
            total += 1
            if data:
                data, system = data
                all_systems.append(system)
                res.append(data)

# print(ans)
# count = Counter(ans)
# print(len(res))
# print(total)
# print(count)

print(len(res), total)
with open(save_file, 'w', encoding='utf-8') as f:
    for item in res:
        f.write(json.dumps(item, ensure_ascii=False) + '\n')
print(save_file)

system_counter = Counter(all_systems)
system_counter = dict(sorted(system_counter.items(), key=lambda x: x[1]))
print(system_counter)
print([i / len(res) * 100 for i in system_counter.values()])

