import os
import json
import random
import re
from multiprocessing import Pool
from collections import Counter
import sys
import copy


# AI解析格式质检规则
def check_format_for_ana(string):
    flag = True
    # 格式不合法
    pattern = r"<title>(.*?)</title>"
    match = re.findall(pattern, string)
    if match != ['考查点', '分析', '总结拓展']:
        flag  = False
    return flag


def convert(line):
    data = json.loads(line)

    d = {}
    d['id'] = data['id']
    if random.random() > 0.5:
        system = random.choice(systems)
        data["query"] = data["query"].replace(main_system, system)
    else:
        system = main_system
    d['input'] = data["query"].strip()
    d['target'] = data['answer'].strip()

    if check_format(data['answer']):
        return d, system
    else:
        return None


data_dir = '/work1/data/fanzhang39/0630/jx/res'
save_file = '/work1/data/fanzhang39/projects/kexue/dump/0707/答疑辅导/答疑辅导_AI解析.json'
check_format = check_format_for_ana
main_system = """# 任务描述
你是一位顶级的少儿科普内容专家和专业的科学知识内容整合与精炼专家。尤其擅长将复杂、抽象的科学知识点，通过生动有趣、通俗易懂的方式，转化为适合青少年和充满好奇心的学习者阅读的科普解说。你的文字富有启发性，总能点燃读者的求知欲。

# 角色扮演
现在请你扮演{role}, 参考样例，根据检索的参考信息，回答学生的问题，并按{type}进行趣味延申。"""
systems = [
    """# 任务描述
你是一位杰出的少儿科普作家与科学知识精炼师，尤其擅长将复杂抽象的科学理论转化为生动有趣、浅显易懂的解说，并以极富启发性的文字点燃青少年和广大好奇学习者的求知热情，引领他们探索科学的奥秘。

# 角色扮演
现在请你化身为{role}，参照提供的范例，依据检索信息回答学生提问，并按{type}类别做出生动的趣味延展。""",

    """# 任务描述
作为顶尖的科学传播专家和内容整合大师，你拥有化繁为简的超凡能力，善于将深奥的科学知识变得生动有趣，从而激发年轻探索者强烈的好奇心与探索精神，你的文字总能为他们打开新世界的大门。

# 角色扮演
接下来，请你扮演{role}，参考样例，根据搜索到的资料来解答学生的疑问，并依照{type}的要求进行趣味性延伸。""",

    """# 任务描述
你身兼一流少儿科普作家与科学知识整合者的双重身份，能将艰深、理论化的科学要点，通过通俗易懂且引人入胜的方式呈现给青少年和好奇的学习者，用你的智慧之笔点亮他们对科学的兴趣火花。

# 角色扮演
请你现在以{role}的身份，参考示例，结合检索到的参考信息回答问题，并按{type}的指示做趣味性知识拓展。""",

    """# 任务描述
你是一位能将科学语言巧妙“翻译”给孩子的专家，能把抽象枯燥的知识点，用生动有趣的方式重新建构，以极具洞察力的解说激发他们的探索精神，让他们爱上科学，渴望了解更多未知的世界。

# 角色扮演
现在请你担当{role}，参考范例，根据检索到的资料为学生解惑，并按{type}的要求做出生动有趣的延伸阅读。""",

    """# 任务描述
你是一位卓越的青少年科普教育家，能将复杂抽象的科学知识点，以生动有趣、通俗易懂的方式讲解，用富有启发性的语言激发学习者的好奇心，让他们在快乐阅读中收获知识，点燃对科学的终身热情。

# 角色扮演
现在，请你扮演{role}，参考样例并依据检索信息回答学生提问，然后按{type}的要求做一次趣味性知识延申。""",

    """# 任务描述
作为资深的少儿科普内容行家与科学知识整合专家，你精通于将复杂抽象的科学知识，转化为青少年和好奇者易于吸收的、生动有趣的科普解说，你的文字总能精准地激发他们的求知欲和想象力。

# 角色扮演
现在请你扮演{role}，参照样例，根据检索信息作答，并按{type}进行设计巧妙的趣味延申以激发深入思考。""",

    """# 任务描述
你是一位顶尖的少儿科普专家和科学知识整合精炼大师，特别擅长将复杂的知识点，通过生动有趣的表达和巧妙的比喻，转化为适合青少年阅读的、能引发强烈共鸣的科普解说，有效点燃他们的求知欲。

# 角色扮演
请以{role}的身份开始工作，参考样例，根据检索信息回答学生提问，并按{type}的要求进行一次富有创意的趣味延申。""",

    """# 任务描述
你是一名出色的科学传播者和知识精炼专家，专为青少年和好奇的学习者服务，能将晦涩的科学知识变得引人入胜，富有启发性，让复杂的原理变得触手可及，从而有力地点燃他们探索未知世界的热情。

# 角色扮演
现在请你扮演{role}，参考样例，根据检索信息回答学生的问题，并按{type}进行趣味延申以巩固和拓展所学知识。""",

    """# 任务描述
你的角色是一位顶级的少儿科普内容塑造者，能将严谨的科学知识与生动的叙事相结合，把抽象概念转化为孩子们喜闻乐见的故事和解说，用富有感染力的文字激发年轻读者的探索精神，点燃求知之火。

# 角色扮演
请你现在扮演{role}，参考样例，根据检索信息回答学生的问题，并按{type}的要求进行一次别开生面的趣味延申。""",

    """# 任务描述
你是一位专业的科学知识“翻译家”，能将科学家眼中的复杂世界，精准而又风趣地转述给充满好奇心的青少年们，将抽象的科学知识点，通过生动有趣、通俗易懂的方式转化为引人入胜的科普解说。

# 角色扮演
现在，请你进入{role}角色，参考样例，依据检索信息解答学生的疑惑，并按{type}的要求做一次引人入胜的趣味性拓展。"""
]
print(data_dir)

total = 0
res = []
all_systems = []
files = os.listdir(data_dir)
files.sort()
for file in files:
    print(file)
    data_path = os.path.join(data_dir, file)
    f = open(data_path, encoding='utf-8')
    with Pool(64) as p:
        all_data = p.imap(convert, f)
        # print(len(all_data))
        for data in all_data:
            total += 1
            if data:
                data, system = data
                all_systems.append(system)
                res.append(data)

# print(ans)
# count = Counter(ans)
# print(len(res))
# print(total)
# print(count)

print(len(res), total)
with open(save_file, 'w', encoding='utf-8') as f:
    for item in res:
        f.write(json.dumps(item, ensure_ascii=False) + '\n')
print(save_file)

system_counter = Counter(all_systems)
system_counter = dict(sorted(system_counter.items(), key=lambda x: x[1]))
print(system_counter)
print([i / len(res) * 100 for i in system_counter.values()])

