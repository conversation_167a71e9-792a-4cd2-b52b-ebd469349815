import json
from collections import defaultdict
from datetime import datetime


def load_json_file(file_path):
    """加载JSON文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载文件 {file_path} 失败: {e}")
        return None

def analyze_data_list(data_list):
    """分析data_list.json的数据结构"""
    print("=== 分析 data_list.json ===")
    
    if not data_list:
        print("data_list为空或加载失败")
        return {}
    
    # 统计各类型数据
    type_count = defaultdict(int)
    question_by_type = defaultdict(set)
    
    for item in data_list:
        # 获取键（类型）
        data_key = list(item.keys())[0]
        type_count[data_key] += 1
        
        # 获取问题
        question = item[data_key][0]['input']['question']
        question_by_type[data_key].add(question)
    
    print(f"总条目数: {len(data_list)}")
    print("各类型统计:")
    for data_type, count in type_count.items():
        print(f"  {data_type}: {count}条")
    
    print("\n各类型独特问题数:")
    for data_type, questions in question_by_type.items():
        print(f"  {data_type}: {len(questions)}个独特问题")
    
    return {
        'total_items': len(data_list),
        'type_count': dict(type_count),
        'question_by_type': {k: list(v) for k, v in question_by_type.items()},
        'unique_questions_total': len(set().union(*question_by_type.values()))
    }

def analyze_sft_data(sft_data):
    """分析sft_data.json的数据结构"""
    print("\n=== 分析 sft_data.json ===")
    
    if not sft_data:
        print("sft_data为空或加载失败")
        return {}
    
    # 从input中提取类型信息
    type_count = defaultdict(int)
    questions = set()
    
    for item in sft_data:
        # 从input文本中提取角色和类型信息
        input_text = item.get('input', '')
        
        # 简单的文本解析来提取类型
        if '居里夫人' in input_text and '小行动' in input_text:
            data_type = '居里夫人_小行动'
        elif '居里夫人' in input_text and '冷知识' in input_text:
            data_type = '居里夫人_冷知识'
        elif '达尔文' in input_text and '小行动' in input_text:
            data_type = '达尔文_小行动'
        elif '达尔文' in input_text and '冷知识' in input_text:
            data_type = '达尔文_冷知识'
        else:
            data_type = '未知类型'
        
        type_count[data_type] += 1
        
        # 提取问题（从input文本中解析）
        lines = input_text.split('\n')
        for line in lines:
            if line.startswith('# 学生输入问题'):
                continue
            if line.strip() and not line.startswith('#') and '?' in line:
                questions.add(line.strip())
                break
    
    print(f"总条目数: {len(sft_data)}")
    print("各类型统计:")
    for data_type, count in type_count.items():
        print(f"  {data_type}: {count}条")
    
    print(f"\n提取到的问题数: {len(questions)}")
    
    return {
        'total_items': len(sft_data),
        'type_count': dict(type_count),
        'questions': list(questions)
    }

def compare_outputs_by_target(data_list, sft_data):
    """通过target字段对比data_list的output和sft_data的target"""
    print("\n=== 基于target字段的精确对比 ===")

    # 提取data_list中的所有output
    data_list_outputs = set()
    data_list_output_map = {}  # output -> 完整数据项

    for item in data_list:
        data_key = list(item.keys())[0]
        output = item[data_key][0]['output']
        data_list_outputs.add(output)
        data_list_output_map[output] = {
            'type': data_key,
            'question': item[data_key][0]['input']['question']
        }

    # 提取sft_data中的所有target
    sft_data_targets = set()
    sft_data_target_map = {}  # target -> 完整数据项

    for item in sft_data:
        target = item['target']
        sft_data_targets.add(target)
        sft_data_target_map[target] = item

    print(f"data_list.json 中的output数量: {len(data_list_outputs)}")
    print(f"sft_data.json 中的target数量: {len(sft_data_targets)}")

    # 找出完全匹配的
    matched = data_list_outputs & sft_data_targets
    print(f"完全匹配的数量: {len(matched)}")

    # 找出data_list中有但sft_data中没有的
    missing_in_sft = data_list_outputs - sft_data_targets
    print(f"data_list中有但sft_data中缺失的: {len(missing_in_sft)}")

    # 找出sft_data中有但data_list中没有的
    extra_in_sft = sft_data_targets - data_list_outputs
    print(f"sft_data中有但data_list中没有的: {len(extra_in_sft)}")

    # 详细分析缺失的数据
    if missing_in_sft:
        print(f"\n❌ 缺失的数据详情（前10条）:")
        missing_list = list(missing_in_sft)[:10]
        for i, output in enumerate(missing_list, 1):
            info = data_list_output_map[output]
            print(f"  {i}. 类型: {info['type']}")
            print(f"     问题: {info['question']}")
            print(f"     输出: {output[:100]}...")
            print()

    # 分析匹配率
    if len(data_list_outputs) > 0:
        match_rate = len(matched) / len(data_list_outputs) * 100
        print(f"\n📊 数据完整性: {match_rate:.2f}%")

        if match_rate == 100:
            print("✅ 所有数据都已正确转换")
        elif match_rate >= 90:
            print("⚠️ 大部分数据已转换，少量缺失")
        else:
            print("❌ 存在大量数据丢失")

    return {
        'total_data_list': len(data_list_outputs),
        'total_sft_data': len(sft_data_targets),
        'matched': len(matched),
        'missing_in_sft': len(missing_in_sft),
        'extra_in_sft': len(extra_in_sft),
        'match_rate': len(matched) / len(data_list_outputs) * 100 if len(data_list_outputs) > 0 else 0
    }

def compare_data(data_list_analysis, sft_data_analysis):
    """对比两个数据集"""
    print("\n=== 数据对比结果 ===")

    # 对比总数
    data_list_total = data_list_analysis.get('total_items', 0)
    sft_data_total = sft_data_analysis.get('total_items', 0)

    print(f"data_list.json 总条目: {data_list_total}")
    print(f"sft_data.json 总条目: {sft_data_total}")
    print(f"差异: {data_list_total - sft_data_total}")

    if data_list_total == sft_data_total:
        print("✅ 总数一致")
    else:
        print("❌ 总数不一致，可能有数据丢失")

    # 对比各类型数量
    print("\n各类型数量对比:")
    data_list_types = data_list_analysis.get('type_count', {})
    sft_data_types = sft_data_analysis.get('type_count', {})

    all_types = set(data_list_types.keys()) | set(sft_data_types.keys())

    for data_type in sorted(all_types):
        data_list_count = data_list_types.get(data_type, 0)
        sft_data_count = sft_data_types.get(data_type, 0)
        diff = data_list_count - sft_data_count

        status = "✅" if diff == 0 else "❌"
        print(f"  {data_type}: {data_list_count} -> {sft_data_count} (差异: {diff}) {status}")

    # 检查是否有遗漏的类型
    missing_in_sft = set(data_list_types.keys()) - set(sft_data_types.keys())
    if missing_in_sft:
        print(f"\n❌ sft_data中缺失的类型: {missing_in_sft}")

    extra_in_sft = set(sft_data_types.keys()) - set(data_list_types.keys())
    if extra_in_sft:
        print(f"\n⚠️ sft_data中多出的类型: {extra_in_sft}")

def main():
    # 文件路径
    data_list_path = r'D:\ProJects\kexue\sft_data\data_list.json'
    sft_data_path = r'D:\ProJects\kexue\sft_data\sft_data.json'

    # 加载数据
    print("正在加载数据文件...")
    data_list = load_json_file(data_list_path)
    sft_data = load_json_file(sft_data_path)

    if data_list is None or sft_data is None:
        print("无法加载数据文件，请检查文件路径")
        return

    # 分析数据
    data_list_analysis = analyze_data_list(data_list)
    sft_data_analysis = analyze_sft_data(sft_data)

    # 对比数据
    compare_data(data_list_analysis, sft_data_analysis)

    # 精确对比target字段
    target_comparison = compare_outputs_by_target(data_list, sft_data)

    # 保存分析结果
    analysis_result = {
        'data_list_analysis': data_list_analysis,
        'sft_data_analysis': sft_data_analysis,
        'target_comparison': target_comparison,
        'comparison_time': str(datetime.now()) if 'datetime' in globals() else 'unknown'
    }

    with open('data_comparison_result.json', 'w', encoding='utf-8') as f:
        json.dump(analysis_result, f, ensure_ascii=False, indent=2)

    print(f"\n分析结果已保存到: data_comparison_result.json")

if __name__ == '__main__':
    main()
