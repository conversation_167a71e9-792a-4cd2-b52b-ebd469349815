import json
import re

# 读取原始数据
with open(r'sft_data.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

# 取第一条数据测试
test_input = data[0]['input']
print("原始input前300字符:")
print(repr(test_input[:300]))
print("\n" + "="*50 + "\n")

# 测试替换
system = """# 任务描述
你是一位杰出的少儿科普作家与科学知识精炼师，尤其擅长将复杂抽象的科学理论转化为生动有趣、浅显易懂的解说，并以极富启发性的文字点燃青少年和广大好奇学习者的求知热情，引领他们探索科学的奥秘。

# 角色扮演
现在请你化身为{role}，参照提供的范例，依据检索信息回答学生提问，并按{type}类别做出生动的趣味延展。"""

# 使用正则表达式匹配任务描述和角色扮演部分
pattern = r'# 任务描述\n你是一位顶级的少儿科普内容专家和专业的科学\s*知识内容整合与精炼专家。[^#]+# 角色扮演：?\n现在请你扮演[^,，]+,\s*参考样例，根据检索的参考信息，回答学生的问题，并按[^。\n]+进行趣味延申。'

result = re.sub(pattern, system, test_input, flags=re.DOTALL)

print("替换后的input前300字符:")
print(repr(result[:300]))

if result != test_input:
    print("\n✅ 替换成功!")
else:
    print("\n❌ 替换失败!")
    
    # 尝试找到匹配的部分
    match = re.search(pattern, test_input, flags=re.DOTALL)
    if match:
        print("找到匹配:", match.group()[:100])
    else:
        print("没有找到匹配")
        
        # 尝试简化的模式
        simple_pattern = r'# 任务描述.*?# 角色扮演.*?进行趣味延申。'
        match2 = re.search(simple_pattern, test_input, flags=re.DOTALL)
        if match2:
            print("简化模式找到匹配:", match2.group()[:100])
        else:
            print("简化模式也没找到匹配")
